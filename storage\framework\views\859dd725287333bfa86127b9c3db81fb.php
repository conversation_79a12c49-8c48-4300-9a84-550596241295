<div>
    <header class="bg-white border-bottom py-2 sticky-top" style="z-index: 1030;">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-12 text-center" style="height: 60px;">
                    <img style="height:100%;" src="<?php echo e(asset('logo.png')); ?>" alt="">
                </div>

            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <!-- Filter Sidebar -->
            <div class="col-md-3 p-4">
                <div class="sticky-top" style="top: 20px;">
                    

                    <h5 class="mb-3">Filter Containers</h5>

                    <!-- Size Filter (Grid 3 per row) -->
                    <div class="filter-section">
                        <h6 class="mb-2">Size</h6>
                        <div class="row g-1">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $sizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="<?php echo e($size); ?>"
                                            wire:model.live="filterSize" id="size-<?php echo e($size); ?>">
                                        <label class="form-check-label" for="size-<?php echo e($size); ?>">
                                            <?php echo e($size); ?>ft
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <!-- Category Filter (Grid 2 per row) -->
                    <div class="filter-section">
                        <h6 class="mb-2">Category</h6>
                        <div class="row g-1">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="<?php echo e($category->id); ?>"
                                            wire:model.live="filterCategory" id="cat-<?php echo e($category->id); ?>">
                                        <label class="form-check-label" for="cat-<?php echo e($category->id); ?>">
                                            <?php echo e($category->title); ?>

                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>

                    <!-- Type Filter -->
                    <div class="filter-section">
                        <h6 class="mb-2">Type</h6>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = ['rental', 'purchase']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="<?php echo e($type); ?>"
                                    wire:model.live="filterType" id="type-<?php echo e($type); ?>">
                                <label class="form-check-label" for="type-<?php echo e($type); ?>">
                                    <?php echo e(ucfirst($type)); ?>

                                </label>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        
                    </div>

                    <!-- Enhanced Price Range -->
                    <div class="filter-section">
                        <h6 class="mb-2">Price Range</h6>
                        <div class="price-range-container">
                            <input type="range" class="form-range price-slider" min="0" max="100000"
                                wire:model.live="priceRange" id="priceRangeSlider" value="0">
                            <div class="price-range-fill" id="priceRangeFill"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>$0</small>
                            <small id="priceValue">$0</small>
                            <small>$100,000</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-8 p-4">
                <!-- Search and Sort Bar -->
                <div class="mb-4">
                    <div class="row g-2 flex-wrap align-items-center">
                        <div class="col-12 col-sm-6 col-md-8 mb-2 mb-md-0">
                            <input type="text" class="form-control" placeholder="Search containers..."
                                wire:model.live="filterSearch">
                        </div>
                        <div class="col-8 col-sm-4 col-md-3 mb-2 mb-md-0">
                            <select class="form-select" wire:model.live="filterSort">
                                <option value="">Sort by</option>
                                <option value="price-asc">Price: Low to High</option>
                                <option value="price-desc">Price: High to Low</option>
                                <option value="size">Size</option>
                            </select>
                        </div>
                        <div class="col-4 col-sm-2 col-md-1 d-flex justify-content-end">
                            <div class="cart-badge cart-btn-responsive d-flex align-items-center justify-content-center w-100 h-100"
                                style="background: #000; color: #fff; border-radius: 8px; min-width: 44px; min-height: 40px; cursor:pointer;"
                                data-bs-toggle="offcanvas" data-bs-target="#cartOffcanvas" aria-controls="cartOffcanvas">
                                <i class="fas fa-shopping-cart fa-lg text-light"></i>
                                <span class="cart-count"><?php echo e(count($cart)); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $containers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $container): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="col-lg-4 col-md-4 mb-4">
                            <div class="card container-card h-100">
                                <div class="position-relative">
                                    <!--[if BLOCK]><![endif]--><?php if($container->image): ?>
                                        <img src="<?php echo e($container->image); ?>" class="card-img-top"
                                            alt="<?php echo e($container->title); ?>" style="height: 200px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                            style="height: 200px;">
                                            <i class="fas fa-box fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <div class="category-tag"><?php echo e($container->category->title ?? 'General'); ?></div>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo e($container->title); ?></h5>
                                    <p class="card-text text-muted"><?php echo e($container->description); ?></p>

                                    <div class="mb-3">
                                        <div class="spec-icon">
                                            <i class="fas fa-ruler-combined"></i>
                                            <?php echo e($container->size->size ?? 'N/A'); ?>ft
                                        </div>
                                        <div class="spec-icon">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo e($container->warehouse->location ?? 'N/A'); ?>

                                        </div>
                                    </div>
                                    <style>
                                        .form-select:focus {
                                            border: none;
                                        }
                                    </style>
                                    <!-- Type Dropdown -->
                                    <div class="mb-2">
                                        <label class="form-label">Type</label>
                                        <select class="form-select form-select-sm outline-none shadow-none"
                                            wire:model.live="selectedType.<?php echo e($container->id); ?>">
                                            <!--[if BLOCK]><![endif]--><?php if($container->type === 'rental' || $container->type === 'both'): ?>
                                                <option value="Rental">Rental</option>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <!--[if BLOCK]><![endif]--><?php if($container->type === 'purchase' || $container->type === 'both'): ?>
                                                <option value="Purchase">Purchase</option>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </select>
                                    </div>

                                    <!-- Duration Dropdown (only for rental) -->
                                    <?php
                                        $currentSelectedType =
                                            $selectedType[$container->id] ?? ucfirst($container->type);
                                        $currentSelectedDuration = $selectedDuration[$container->id] ?? 1;
                                    ?>

                                    <!--[if BLOCK]><![endif]--><?php if($currentSelectedType === 'Rental'): ?>
                                        <div class="mb-2">
                                            <label class="form-label">Duration</label>
                                            <select class="form-select form-select-sm"
                                                wire:model.live="selectedDuration.<?php echo e($container->id); ?>">
                                                <option value="1">1 month</option>
                                                <option value="2">2 months</option>
                                                <option value="3">3 months</option>
                                                <option value="4">4 months</option>
                                                <option value="5">5 months</option>
                                                <option value="6">6 months</option>
                                                <option value="7">7 months</option>
                                                <option value="8">8 months</option>
                                                <option value="9">9 months</option>
                                                <option value="10">10 months</option>
                                                <option value="11">11 months</option>
                                                <option value="12">12 months</option>
                                            </select>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <div class="mt-auto">
                                        <div class="mb-3">
                                            <!--[if BLOCK]><![endif]--><?php if($currentSelectedType == 'Rental'): ?>
                                                <span
                                                    class="h5 text-dark">$<?php echo e(number_format($container->rental_price * $currentSelectedDuration)); ?>

                                                    for <?php echo e($currentSelectedDuration); ?>

                                                    month<?php echo e($currentSelectedDuration > 1 ? 's' : ''); ?></span>
                                            <?php else: ?>
                                                <span
                                                    class="h5 text-dark">$<?php echo e(number_format($container->purchase_price)); ?>

                                                    to own</span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <!--[if BLOCK]><![endif]--><?php if(($container->stock ?? 1) > 0): ?>
                                            <button class="btn btn-dark w-100"
                                                wire:click="addToCart(<?php echo e($container->id); ?>, '<?php echo e($currentSelectedType); ?>', <?php echo e($currentSelectedDuration); ?>)">
                                                <i class="fas fa-plus me-2"></i>Add to Cart 
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-out-of-stock w-100" disabled>
                                                <i class="fas fa-times me-2"></i>Out of Stock
                                            </button>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="col-12 text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No containers found</h5>
                            <p class="text-muted">Try adjusting your filters to find what you're looking for.</p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            <!-- Cart Offcanvas (Side Modal) -->
            <div class="offcanvas offcanvas-end" tabindex="-1" id="cartOffcanvas"
                aria-labelledby="cartOffcanvasLabel" style="width: 400px;">
                <div class="offcanvas-header">
                    <h5 class="offcanvas-title" id="cartOffcanvasLabel">Shopping Cart</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="offcanvas"
                        aria-label="Close"></button>
                </div>
                <div class="offcanvas-body d-flex flex-column h-100">
                    <!--[if BLOCK]><![endif]--><?php if(count($cart) > 0): ?>
                        <p class="text-muted"><?php echo e(count($cart)); ?> items</p>

                        <?php
                            $subtotal = 0;
                            foreach ($cart as $item) {
                                $subtotal += $item['price'];
                            }
                            $tax = $subtotal * 0.13;
                            $total = $subtotal + $tax;
                        ?>

                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cart; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex align-items-start mb-3 pb-3 border-bottom"
                                data-cart-key="<?php echo e($key); ?>">
                                <img src="<?php echo e($item['image'] ?? 'https://via.placeholder.com/50'); ?>"
                                    class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo e($item['name']); ?></h6>
                                    <div class="small text-muted">
                                        <?php echo e($item['type']); ?>

                                        <?php if($item['type'] === 'Rental'): ?>
                                            (<?php echo e($item['rentalMonths']); ?>

                                            month<?php echo e($item['rentalMonths'] > 1 ? 's' : ''); ?>)
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                    <div class="small text-muted"><?php echo e($item['size']); ?>ft • <?php echo e($item['warehouse']); ?>

                                    </div>
                                    <div class="input-group cart-qty-controls mt-2">
                                        <button class="btn btn-outline-secondary" type="button"
                                            onclick="decrementQuantity('<?php echo e($key); ?>')">-</button>
                                        <input type="text" class="form-control text-center"
                                            value="<?php echo e($item['quantity'] ?? 1); ?>" readonly
                                            data-quantity="<?php echo e($item['quantity'] ?? 1); ?>" style="background:#fff;">
                                        <button class="btn btn-outline-secondary" type="button"
                                            onclick="incrementQuantity('<?php echo e($key); ?>')">+</button>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold"
                                        data-unit-price="<?php echo e($item['unit_price'] ?? ($item['price'] ?? 0)); ?>"
                                        data-total-price="<?php echo e($item['price']); ?>">
                                        $<?php echo e(number_format($item['price'])); ?></div>
                                    <button class="btn btn-link text-danger p-0"
                                        wire:click="removeFromCart('<?php echo e($key); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                        <div class="mt-auto">
                            <div class="offcanvas-footer bg-light p-3 rounded shadow-sm">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal</span>
                                    <span id="subtotal">$<?php echo e(number_format($subtotal, 2)); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Tax (13%)</span>
                                    <span id="tax">$<?php echo e(number_format($tax, 2)); ?></span>
                                </div>
                                <hr class="my-2">
                                <div class="d-flex justify-content-between">
                                    <span class="fw-bold">Total</span>
                                    <span class="fw-bold h5 mb-0"
                                        id="total">$<?php echo e(number_format($total, 2)); ?></span>
                                </div>
                            </div>
                            <div class="d-grid gap-2 mt-3">
                                <button class="btn btn-outline-danger btn-sm" wire:click="clearCart">
                                    Clear Cart
                                </button>
                                <button class="btn btn-dark" wire:click="checkout">
                                    Proceed to Checkout
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4 mt-auto">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Your cart is empty</h6>
                            <p class="text-muted small">Add some containers to get started!</p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
        <footer class="bg-light border-top py-4 mt-5">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                       <img src="<?php echo e(asset('logo.png')); ?>" height="50px" alt="">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        © <?php echo e(date('Y')); ?> Storage-Tech. All rights reserved. 
                    </small>
                </div>
            </div>
        </div>
    </footer>
    <style>
        .cart-badge {
            position: relative;
            cursor: pointer;
        }

        /* Responsive cart button for mobile */
        .cart-btn-responsive {
            min-width: 44px;
            min-height: 44px;
            border-radius: 8px;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            min-width: 20px;
            text-align: center;
        }

        .container-card {
            transition: transform 0.2s;
        }

        .container-card:hover {
            transform: translateY(-2px);
        }

        @media (max-width: 576px) {
            .cart-btn-responsive {
                margin-top: 0.5rem;
                margin-bottom: 0.5rem;
            }
        }

        .category-tag {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(1, 1, 1, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .spec-icon {
            margin-bottom: 8px;
        }

        .spec-icon i {
            width: 20px;
            color: #6c757d;
        }

        .filter-section {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .btn-out-of-stock {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        /* Enhanced Price Range Styles */
        .price-range-container {
            position: relative;
            height: 24px;
            margin: 10px 0;
        }

        .form-range {
            position: relative;
            z-index: 2;
            background: transparent;
            -webkit-appearance: none;
            appearance: none;
            height: 8px;
            border-radius: 4px;
        }

        .form-range::-webkit-slider-track {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
        }

        .form-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #6c757d;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .form-range::-moz-range-track {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            border: none;
        }

        .form-range::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #6c757d;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .price-range-fill {
            position: absolute;
            left: 0;
            top: 50%;
            height: 8px;
            background: #6c757d;
            z-index: 1;
            pointer-events: none;
            border-radius: 4px;
            transform: translateY(-50%);
            width: 0%;
            transition: width 0.2s ease;
        }

        /* Cart quantity controls */
        .cart-qty-controls {
            max-width: 120px;
            display: flex;
            align-items: stretch;
            height: 30px;
        }

        .cart-qty-controls .btn {
            width: 30px;
            height: 30px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #111 !important;
            color: #fff !important;
            border: none;
            border-radius: 0 !important;
            font-size: 1.25rem;
            transition: background 0.2s;
        }

        .cart-qty-controls .btn:hover,
        .cart-qty-controls .btn:focus {
            background: #333 !important;
            color: #fff !important;
        }

        .cart-qty-controls input {
            text-align: center;
            background: white !important;
            border: 1px solid #ddd;
            border-radius: 0;
            height: 30px;
            width: 48px;
            font-size: 0.9rem;
            box-shadow: none;
        }

        .cart-qty-controls input:focus {
            box-shadow: none;
            outline: none;
            border: none;
        }
    </style>

    <!-- Enhanced JavaScript for cart updates and range slider -->
    <script>
        // Price range slider functionality
        function updatePriceRangeFill() {
            const slider = document.getElementById('priceRangeSlider');
            const fill = document.getElementById('priceRangeFill');
            const priceValue = document.getElementById('priceValue');

            if (slider && fill && priceValue) {
                const percent = ((slider.value - slider.min) / (slider.max - slider.min)) * 100;
                fill.style.width = percent + '%';

                // Update price display
                const value = parseInt(slider.value);
                priceValue.textContent = '$' + value.toLocaleString();
            }
        }

        // Cart quantity management with Livewire integration
        function incrementQuantity(itemKey) {
            const cartItem = document.querySelector(`[data-cart-key="${itemKey}"]`);
            if (!cartItem) return;

            const input = cartItem.querySelector('input[data-quantity]');
            const priceDiv = cartItem.querySelector('.fw-bold');
            const unitPrice = parseFloat(priceDiv.getAttribute('data-unit-price')) || 0;

            let quantity = parseInt(input.getAttribute('data-quantity')) || 1;
            quantity++;

            // Update UI immediately for better user experience
            input.value = quantity;
            input.setAttribute('data-quantity', quantity);

            const totalPrice = unitPrice * quantity;
            // priceDiv.textContent = '$' + totalPrice.toLocaleString();
            priceDiv.setAttribute('data-total-price', totalPrice);

            // Update cart totals
            updateCartTotals();

            // Call Livewire backend method
            if (typeof Livewire !== 'undefined') {
                Livewire.emit('incrementQuantity', itemKey);
            }
        }

        function decrementQuantity(itemKey) {
            const cartItem = document.querySelector(`[data-cart-key="${itemKey}"]`);
            if (!cartItem) return;

            const input = cartItem.querySelector('input[data-quantity]');
            const priceDiv = cartItem.querySelector('.fw-bold');
            const unitPrice = parseFloat(priceDiv.getAttribute('data-unit-price')) || 0;

            let quantity = parseInt(input.getAttribute('data-quantity')) || 1;
            if (quantity <= 1) return; // Don't go below 1

            quantity--;

            // Update UI immediately for better user experience
            input.value = quantity;
            input.setAttribute('data-quantity', quantity);

            const totalPrice = unitPrice * quantity;
            // priceDiv.textContent = '$' + totalPrice.toLocaleString();
            priceDiv.setAttribute('data-total-price', totalPrice);

            // Update cart totals
            updateCartTotals();

            // Call Livewire backend method
            if (typeof Livewire !== 'undefined') {
                Livewire.emit('decrementQuantity', itemKey);
            }
        }

        function updateCartTotals() {
            let subtotal = 0;

            // Calculate subtotal from all cart items
            document.querySelectorAll('[data-cart-key]').forEach(item => {
                const totalPrice = parseFloat(item.querySelector('.fw-bold').getAttribute('data-total-price')) || 0;
                subtotal += totalPrice;
            });

            const tax = subtotal * 0.13;
            const total = subtotal + tax;

            // Update display
            const subtotalEl = document.getElementById('subtotal');
            const taxEl = document.getElementById('tax');
            const totalEl = document.getElementById('total');

            if (subtotalEl) subtotalEl.textContent = '$' + subtotal.toLocaleString('en-US', {
                minimumFractionDigits: 2
            });
            if (taxEl) taxEl.textContent = '$' + tax.toLocaleString('en-US', {
                minimumFractionDigits: 2
            });
            if (totalEl) totalEl.textContent = '$' + total.toLocaleString('en-US', {
                minimumFractionDigits: 2
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize price range slider
            const slider = document.getElementById('priceRangeSlider');
            if (slider) {
                updatePriceRangeFill();
                slider.addEventListener('input', updatePriceRangeFill);
                slider.addEventListener('change', updatePriceRangeFill);
            }

            // Initialize cart totals
            updateCartTotals();
        });

        // Listen for Livewire updates
        if (typeof Livewire !== 'undefined') {
            document.addEventListener('livewire:load', function() {
                Livewire.on('cart-updated', function(data) {
                    // Update cart count in header
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount && data.count !== undefined) {
                        cartCount.textContent = data.count;
                    }

                    // Update cart totals after Livewire updates
                    setTimeout(updateCartTotals, 100);
                });
            });

            // Re-initialize after Livewire updates
            document.addEventListener('livewire:update', function() {
                updatePriceRangeFill();
                updateCartTotals();
            });
        }
    </script>
</div>
<?php /**PATH C:\xampp\htdocs\containers\resources\views/livewire/container-catalog.blade.php ENDPATH**/ ?>