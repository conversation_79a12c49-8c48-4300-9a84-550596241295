<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Container Booking Platform</title>
	<link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700&display=swap" rel="stylesheet">
	<link rel="icon" href="https://storage-tech.ca/wp-content/uploads/2023/07/cropped-favicon-32x32.png">
	<style>
		:root {
			--primary: #1a2236;
			--accent: #2ec4b6;
			--accent-dark: #1e9e8e;
			--bg: #f7f9fa;
			--border: #e0e0e0;
			--text: #222;
			--radius: 12px;
			--sidebar-width: 270px;
			--cart-width: 350px;
			--header-height: 72px;
			--shadow: 0 2px 12px rgba(26, 34, 54, 0.07);
			--card-min-height: 420px;
			--banner-height: 260px;
			--category-icon-size: 54px;
		}

		* {
			box-sizing: border-box;
		}

		body {
			margin: 0;
			font-family: 'Montserrat', Arial, sans-serif;
			background: var(--bg);
			color: var(--text);
			min-height: 100vh;
			display: flex;
			flex-direction: column;
		}

		header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: var(--header-height);
			padding: 0 5rem;
			background-color: #f8f8f8;
			box-shadow: var(--shadow);
			position: sticky;
			top: 0;
			z-index: 10;
		}

		.logo {
			display: flex;
			align-items: center;
			gap: 0.7rem;
			font-size: 1.6rem;
			font-weight: 700;
			color: var(--primary);
			letter-spacing: 1px;
		}

		.logo img {
			height: 38px;
			width: auto;
			vertical-align: middle;
		}

		.cart-btn {
			position: relative;
			background: var(--accent);
			border: none;
			color: #fff;
			font-size: 1.6rem;
			border-radius: 50%;
			width: 48px;
			height: 48px;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			transition: background 0.2s;
			box-shadow: 0 2px 8px rgba(46, 196, 182, 0.08);
		}

		.cart-btn:hover {
			background: var(--accent-dark);
		}

		.cart-count {
			position: absolute;
			top: 6px;
			right: 6px;
			background: #fff;
			color: var(--accent);
			border-radius: 50%;
			width: 22px;
			height: 22px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 0.95rem;
			font-weight: 700;
			border: 2px solid var(--accent);
			box-shadow: 0 1px 4px rgba(46, 196, 182, 0.10);
		}

		.banner {
			width: 100%;
			height: var(--banner-height);
			background: url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1200&q=80') center/cover no-repeat;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			position: relative;
			margin-bottom: 0.5rem;
		}

		.banner-overlay {
			background: rgba(26, 34, 54, 0.62);
			color: #fff;
			padding: 2.5rem 3rem;
			border-radius: 0 0 var(--radius) var(--radius);
			max-width: 600px;
			margin-left: 2.5rem;
			box-shadow: 0 4px 24px rgba(26, 34, 54, 0.13);
		}

		.banner-title {
			font-size: 2.1rem;
			font-weight: 700;
			margin-bottom: 0.7rem;
			letter-spacing: 1px;
		}

		.banner-desc {
			font-size: 1.13rem;
			font-weight: 500;
			margin-bottom: 0.5rem;
		}

		.categories-row {
			display: flex;
			gap: 2.5rem;
			justify-content: center;
			align-items: center;

			padding: 1.2rem 0 1.2rem 0;

			margin-bottom: 1.5rem;
			flex-wrap: wrap;
		}

		.category-btn {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 0.4rem;
			background: none;
			border: none;
			cursor: pointer;
			color: var(--primary);
			font-size: 1.01rem;
			font-weight: 600;
			transition: color 0.2s;
			min-width: 90px;
		}

		.category-btn.active,
		.category-btn:hover {
			color: var(--accent);
		}

		.category-icon {
			width: var(--category-icon-size);
			height: var(--category-icon-size);
			object-fit: contain;
			margin-bottom: 0.2rem;
			background: #f7f9fa;
			border-radius: 50%;
			border: 1.5px solid #e0e0e0;
			padding: 0.5rem;
			transition: border 0.2s;
		}

		.category-btn.active .category-icon,
		.category-btn:hover .category-icon {
			border: 2px solid var(--accent);
		}

		.main-content {
			display: flex;
			flex: 1;
			min-height: 0;
			max-width: 95%;
			margin: 0 auto;
			width: 95%;
			padding: 2rem 0;
			gap: 2rem;
		}

		aside.filters {
			width: var(--sidebar-width);
			padding: 2rem 1.2rem 1.2rem 1.5rem;
			min-width: 300px;
			max-width: 150vw;
			margin-top: 0.5rem;
			height: fit-content;
			transition: transform 0.3s;
			display: flex;
			flex-direction: column;
			gap: 1.2rem;
		}

		.filters h2 {
			font-size: 1.15rem;
			margin-top: 0;
			margin-bottom: 0.7rem;
			letter-spacing: 0.5px;
			color: var(--primary);
			font-weight: 600;
		}

		.filter-group {
			margin-bottom: 0.7rem;
			padding-bottom: 0.7rem;
			border-bottom: 1px solid #f0f0f0;
		}

		.filter-group:last-child {
			border-bottom: none;
		}

		.filter-group label {
			display: block;
			margin-bottom: 0.3rem;
			font-size: 0.9rem;
			font-weight: 500;
			color: var(--primary);
		}

		.filter-group input,
		.filter-group select {
			width: 100%;
			padding: 0.45rem 0.6rem;
			border: 1.5px solid var(--border);
			border-radius: var(--radius);
			margin-bottom: 0.5rem;
			font-size: 0.8rem;
			background: #fafbfc;
			font-family: inherit;
			transition: border 0.2s;
		}

		.filter-group input:focus,
		.filter-group select:focus {
			border-color: var(--accent);
			outline: none;
		}

		.filter-group .filter-options {
			display: flex;
			flex-wrap: wrap;
			gap: 0.5rem;
		}

		.filter-group .filter-options label {
			display: flex;
			align-items: center;
			font-size: 0.8rem;
			background: #f2f2f2;
			border-radius: var(--radius);
			padding: 0.2rem 0.7rem;
			cursor: pointer;
			font-weight: 500;
			color: var(--primary);
			border: 1px solid #eaeaea;
			transition: background 0.2s, border 0.2s;
		}

		.filter-group .filter-options input[type="checkbox"] {
			margin-right: 0.4rem;
		}

		.sort-search {
			display: flex;
			gap: 0.7rem;
			margin-bottom: 1.5rem;
			align-items: center;
		}

		.sort-search input[type="search"] {
			flex: 1;
			padding: 0.45rem 0.7rem;
			border: 1.5px solid var(--border);
			border-radius: var(--radius);
			font-size: 1rem;
			background: #fafbfc;
			font-family: inherit;
			transition: border 0.2s;
		}

		.sort-search input[type="search"]:focus {
			border-color: var(--accent);
			outline: none;
		}

		.sort-search select {
			padding: 0.45rem 0.7rem;
			border: 1.5px solid var(--border);
			border-radius: var(--radius);
			font-size: 1rem;
			background: #fafbfc;
			font-family: inherit;
			transition: border 0.2s;
		}

		.sort-search select:focus {
			border-color: var(--accent);
			outline: none;
		}

		.products {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 1rem;
		}

		@media (max-width: 1100px) {
			.products {
				grid-template-columns: repeat(2, 1fr);
			}
		}

		@media (max-width: 700px) {
			.products {
				grid-template-columns: 1fr;
			}
		}

		.product-card {
			display: flex;
			flex-direction: column;
			background: #fff;
			border-radius: 5px;
			box-shadow: 0 2px 5px rgba(26, 34, 54, 0.10);
			overflow: hidden;
			min-height: 480px;
			height: 100%;
			position: relative;
			padding: 0;
			margin: 0;
		}

		.product-badge {
			position: absolute;
			top: 18px;
			left: 18px;
			background: #f5f7fa;
			color: #222;
			font-size: 0.93rem;
			font-weight: 600;
			border-radius: 16px;
			padding: 0.3rem 1.1rem;
			z-index: 2;
			letter-spacing: 0.5px;
			box-shadow: 0 2px 8px rgba(26, 34, 54, 0.08);
			border: 1px solid #e0e0e0;
		}

		.product-img {
			width: 100%;
			height: 160px;
			object-fit: cover;
			background: #eaeaea;
			border-bottom: 1.5px solid #f0f0f0;
		}

		.product-info {
			display: flex;
			flex-direction: column;
			flex: 1;
			padding: 1.3rem 1.3rem 0 1.3rem;
			gap: 0.7rem;
		}

		.product-title {
			font-size: 1.13rem;
			font-weight: 700;
			color: #222;
			margin-bottom: 0.2rem;
			margin-top: 0.2rem;
			text-align: left;
		}

		.product-desc {
			font-size: 0.99rem;
			color: #6b7280;
			margin-bottom: 0.5rem;
			font-weight: 500;
			text-align: left;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.product-meta {
			display: flex;
			gap: 1.2rem;
			font-size: 0.97rem;
			color: #6b7280;
			align-items: center;
			margin-bottom: 0.2rem;
		}

		.product-meta span {
			display: flex;
			align-items: center;
			gap: 0.3rem;
		}

		.meta-icon {
			width: 18px;
			height: 18px;
			opacity: 0.7;
		}

		.product-form-row {
			display: flex;
			flex-direction: column;
			gap: 0.2rem;
			margin-bottom: 0.2rem;
		}

		.product-label {
			font-size: 0.97rem;
			color: #222;
			font-weight: 600;
			margin-bottom: 0.2rem;
			text-align: left;
		}

		.modern-select,
		.modern-dropdown {
			width: 100%;
			padding: 0.5rem 0.5rem;
			border: 1.5px solid #e0e0e0;
			border-radius: 10px;
			font-size: 1rem;
			background: #f5f7fa;
			font-family: inherit;
			font-weight: 600;
			color: #222;
			margin-bottom: 0.2rem;
			transition: border 0.2s;
			appearance: none;
			outline: none;
			box-shadow: none;
		}

		.modern-select:focus,
		.modern-dropdown:focus {
			border-color: #2ec4b6;
		}

		.product-footer {
			margin-top: auto;
			padding: 1rem 1rem 1rem 1rem;
			display: flex;
			flex-direction: column;
			gap: 0.7rem;
			border-top: 1px solid #f0f0f0;
			background: #fff;
		}

		.product-price {
			font-size: 1.35rem;
			font-weight: 700;
			color: #222;
			margin-bottom: 0.1rem;
			text-align: left;
		}

		.product-price .per-month {
			font-size: 0.97rem;
			color: #6b7280;
			font-weight: 500;
			margin-left: 0.2rem;
		}

		.add-cart-btn {
			width: 80%;
			margin: 0px auto;
			background: #111827;
			color: #fff;
			border: none;
			border-radius: 2px;
			padding: 0.5rem 0;
			font-size: 1rem;
			font-weight: 500;
			cursor: pointer;
			transition: background 0.2s;
			letter-spacing: 0.5px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 0.7rem;
			margin-top: 0.2rem;
		}

		.add-cart-btn:hover {
			background: #aecfcb;
			color: #111827;
		}

		/* Cart styles */
		aside.cart {
			width: var(--cart-width);
			padding: 2rem 1.5rem 1.2rem 1.2rem;
			min-width: 300px;
			max-width: 200vw;
			display: flex;
			flex-direction: column;
			margin-top: 0.5rem;
			height: fit-content;
			transition: transform 0.3s;
		}

		.cart-items {
			flex: 1;
			overflow-y: auto;
			margin-bottom: 1.2rem;
			max-height: 340px;
		}

		.cart-item {
			border-bottom: 1.5px solid var(--border);
			padding: 0.7rem 0 0.7rem 0;
			display: flex;
			gap: 0.7rem;
			align-items: flex-start;
		}

		.cart-item-img {
			width: 60px;
			height: 40px;
			object-fit: cover;
			border-radius: 5px;
			background: #eaeaea;
			border: 1px solid #f0f0f0;
		}

		.cart-item-info {
			flex: 1;
		}

		.cart-item-title {
			font-size: 1.01rem;
			font-weight: 600;
			margin-bottom: 0.2rem;
			color: var(--primary);
		}

		.cart-item-meta {
			font-size: 0.95rem;
			color: #888;
			margin-bottom: 0.2rem;
			font-weight: 500;
		}

		.cart-item-remove {
			background: none;
			border: none;
			color: var(--accent);
			font-size: 1.2rem;
			cursor: pointer;
			margin-left: 0.5rem;
			transition: color 0.2s;
		}

		.cart-item-remove:hover {
			color: var(--primary);
		}

		.cart-item-delivery {
			font-size: 0.95rem;
			color: var(--accent);
			margin-bottom: 0.2rem;
			font-weight: 600;
		}

		.cart-summary {
			border-top: 1.5px solid var(--border);
			padding-top: 1rem;
			font-size: 1.01rem;
		}

		.cart-summary-row {
			display: flex;
			justify-content: space-between;
			margin-bottom: 0.5rem;
			font-weight: 600;
		}

		.cart-checkout-btn {
			width: 100%;
			background: var(--accent);
			color: #fff;
			border: none;
			border-radius: var(--radius);
			padding: 0.9rem 0;
			font-size: 1.13rem;
			font-weight: 700;
			margin-top: 1rem;
			cursor: pointer;
			transition: background 0.2s;
			letter-spacing: 0.5px;
			box-shadow: 0 1px 4px rgba(46, 196, 182, 0.10);
		}

		.cart-checkout-btn:disabled {
			background: #b2eae3;
			color: #fff;
			cursor: not-allowed;
		}

		.cart-checkout-btn:hover:not(:disabled) {
			background: var(--primary);
			color: #fff;
		}

		/* Mobile fixed buttons */
		.mobile-fixed-btns {
			display: none;
		}

		@media (max-width: 900px) {
			.main-content {
				flex-direction: column;
				gap: 1.5rem;
			}

			aside.filters,
			aside.cart {
				width: 100vw;
				min-width: 0;
				max-width: 100vw;
				border-radius: var(--radius);
				box-shadow: var(--shadow);
				border: none;
				padding: 1rem;
				margin-top: 0;
			}

			aside.filters {
				order: 1;
			}

			main {
				order: 2;
			}

			aside.cart {
				order: 3;
			}

			.mobile-fixed-btns {
				display: flex;
				position: fixed;
				bottom: 0;
				left: 0;
				width: 100vw;
				background: #fff;
				box-shadow: 0 -2px 12px rgba(26, 34, 54, 0.07);
				z-index: 100;
				justify-content: space-between;
				padding: 0.7rem 1.2rem;
				gap: 1rem;
			}

			.mobile-fixed-btns button {
				flex: 1;
				background: var(--primary);
				color: #fff;
				border: none;
				border-radius: var(--radius);
				padding: 0.8rem 0;
				font-size: 1.1rem;
				font-weight: 600;
				cursor: pointer;
				transition: background 0.2s;
			}

			.mobile-fixed-btns button.active,
			.mobile-fixed-btns button:hover {
				background: var(--accent);
				color: var(--primary);
			}
		}

		@media (max-width: 700px) {
			.main-content {
				flex-direction: column;
				gap: 1rem;
			}

			aside.filters,
			aside.cart {
				width: 100vw;
				min-width: 0;
				max-width: 100vw;
				border-radius: var(--radius);
				box-shadow: var(--shadow);
				border: none;
				padding: 1rem;
				margin-top: 0;
			}

			.products {
				grid-template-columns: 1fr;
			}

			header {
				padding: 0 1rem;
			}
		}

		footer {
			background: var(--primary);
			color: #fff;
			text-align: center;
			padding: 1.2rem 0;
			font-size: 1.01rem;
			letter-spacing: 0.5px;
			margin-top: auto;
			font-weight: 500;
		}
	</style>
	@livewireStyles
</head>

<body>
	<header class="bg-white border-bottom py-2 sticky-top" style="z-index: 1030;">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-12 text-center" style="height: 60px;">
                    <img style="height:100%;" src="{{ asset('logo.png') }}" alt="">
                </div>

            </div>
        </div>
    </header>
	<div class="categories-row" id="categoriesRow">
		<!-- Main categories with icons/images -->
	</div>
	<main x-data="{ screen: 'catalog' }" class="py-4">
		<div class="d-flex justify-content-center mb-4" style="gap: 1rem;">
			<button @click="screen = 'catalog'" :class="{'btn btn-primary': screen === 'catalog', 'btn btn-outline-primary': screen !== 'catalog'}">Browse Containers</button>
			<button @click="screen = 'checkout'" :class="{'btn btn-primary': screen === 'checkout', 'btn btn-outline-primary': screen !== 'checkout'}">Checkout</button>
		</div>
		<div x-show="screen === 'catalog'">
			@livewire('container-catalog')
		</div>
		<div x-show="screen === 'checkout'">
			@livewire('checkout')
		</div>
	</main>
	<div class="mobile-fixed-btns">
		<button id="mobileFilterBtn">Filters</button>
		<button id="mobileCartBtn">Cart</button>
	</div>
	    <footer class="bg-light border-top py-4 mt-5">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                       <img src="{{ asset('logo.png') }}" height="50px" alt="">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        © {{ date('Y') }} Storage-Tech. All rights reserved. 
                    </small>
                </div>
            </div>
        </div>
    </footer>
	@livewireScripts
	<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</body>

</html>
