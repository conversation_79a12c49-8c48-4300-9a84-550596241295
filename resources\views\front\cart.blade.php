<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Storage-Tech</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cart-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            transition: box-shadow 0.2s;
        }
        .cart-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .quantity-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 1px solid #dee2e6;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .quantity-btn:hover {
            background: #f8f9fa;
        }
        .tag-pill {
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-right: 5px;
        }
        .order-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .delivery-calculator {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
    @livewireStyles
</head>
<body>
    <!-- Header -->
    <header class="bg-white border-bottom py-2 sticky-top" style="z-index: 1030;">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-12 text-center" style="height: 60px;">
                    <img style="height:100%;" src="{{ asset('logo.png') }}" alt="">
                </div>

            </div>
        </div>
    </header>
    <div class="container-fluid py-4">
        <!-- Navigation -->
        <div class="row d-flex align-items-center mb-4">
            <div class="col-12">
                <div class="d-flex align-items-center justify-center">
                    <a href="{{ route('catalog') }}" class="btn btn-link text-dark text-decoration-none me-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Cart
                    </a>
                    <div class="" style="line-height: 0.5rem;">
                        <h2 class="mb-0">Checkout</h2><br>
                        <p class="text-muted mb-0">Complete your order</p>
                    </div>
                </div>
                {{-- <p class="text-muted mt-2">{{ count($cart ?? []) }} item{{ count($cart ?? []) !== 1 ? 's' : '' }} in your cart.</p> --}}
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                @livewire('cart-items')
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                @livewire('cart-summary')
            </div>
        </div>
    </div>

    <!-- Footer -->
        <footer class="bg-light border-top py-4 mt-5">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                       <img src="{{ asset('logo.png') }}" height="50px" alt="">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        © {{ date('Y') }} Storage-Tech. All rights reserved. 
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    @livewireScripts

    <script>
        document.addEventListener('livewire:load', function () {
            // Debug: Log when delivery is calculated
            Livewire.on('delivery-calculated', function (data) {
                console.log('Delivery calculated in layout:', data);
            });

            // Debug: Log when cart is updated
            Livewire.on('cart-updated', function (data) {
                console.log('Cart updated in layout:', data);
            });
        });
    </script>
</body>
</html>
