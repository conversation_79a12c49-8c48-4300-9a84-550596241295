<div>


    <!-- Cart Items -->
    @if (count($cart) > 0)
        @foreach ($cart as $key => $item)
            <div class="cart-item p-4 mb-3">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <img src="{{ $item['image'] ?? 'https://via.placeholder.com/100' }}" class="img-fluid rounded"
                            alt="{{ $item['name'] }}" style="width: 100px; height: 100px; object-fit: cover;">
                    </div>

                    <div class="col-md-4">
                        <h6 class="mb-1">{{ $item['name'] }}</h6>
                        <p class="text-muted small mb-2">
                            {{ $item['description'] ?? 'Perfect for residential storage needs or small business inventory.' }}
                        </p>

                        <div class="mb-2">
                            <span class="tag-pill">{{ $item['size'] }}ft</span>
                            <span class="tag-pill">{{ strtolower($item['category'] ?? 'storage') }}</span>
                            <span class="tag-pill">{{ $item['rentalMonths'] ?? 1 }}mo</span>
                            <span class="tag-pill">Delivery: ${{ number_format($item['deliveryCost'] ?? 0, 2) }}</span>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-dark me-2">{{ $item['type'] }}</span>
                            @if ($item['type'] === 'Rental')
                                <span class="badge bg-secondary">{{ $item['rentalMonths'] }}
                                    month{{ $item['rentalMonths'] > 1 ? 's' : '' }}</span>
                            @endif
                        </div>

                        @if ($item['type'] === 'Rental')
                            <!-- Rental: Control months (1-12) -->
                            <div class="d-flex align-items-center gap-2" style="margin-top: 10px;">
                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateRentalMonths('{{ $key }}', {{ max(1, ($item['rentalMonths'] ?? 1) - 1) }})"
                                    {{ ($item['rentalMonths'] ?? 1) <= 1 ? 'disabled' : '' }}>-</button>

                                <input type="text" min="1" max="12"
                                    wire:change="updateRentalMonths('{{ $key }}', $event.target.value)"
                                    value="{{ $item['rentalMonths'] ?? 1 }}" class="form-control text-center"
                                    style="width: 70px; font-size: 12px;" />

                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateRentalMonths('{{ $key }}', {{ min(12, ($item['rentalMonths'] ?? 1) + 1) }})"
                                    {{ ($item['rentalMonths'] ?? 1) >= 12 ? 'disabled' : '' }}>+</button>
                            </div>
                            <small class="text-muted">Months (1-12)</small>
                        @else
                            <!-- Purchase: Control quantity -->
                            <div class="d-flex align-items-center gap-2" style="margin-top: 10px;">
                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateQuantity('{{ $key }}', {{ ($item['qty'] ?? 1) - 1 }})">-</button>

                                <input type="text" min="1"
                                    wire:change="updateQuantity('{{ $key }}', $event.target.value)"
                                    value="{{ $item['qty'] ?? 1 }}" class="form-control text-center"
                                    style="width: 70px; font-size: 12px;" />

                                <button class="btn btn-dark btn-sm px-3"
                                    wire:click="updateQuantity('{{ $key }}', {{ ($item['qty'] ?? 1) + 1 }})">+</button>
                            </div>
                            <small class="text-muted">Quantity</small>
                        @endif

                    </div>

                    <div class="col-md-2 text-end">
                        <button class="btn btn-link text-danger p-0 mb-2"
                            wire:click="removeItem('{{ $key }}')">
                            <i class="fas fa-trash"></i>
                        </button>


                        <div class="fw-bold">
                            <span id="ProductPrice" style="display: none;">
                                ${{ number_format($item['price'], 2) }}
                            </span>
                            <span id="ProductPriceTotal">${{ number_format($item['price'] * $item['qty'], 2) }}</span>
                        </div>
                        <small class="text-muted">
                            {{ $item['type'] === 'Rental' ? 'Rental cost' : 'Purchase cost' }}
                        </small>
                    </div>
                </div>
            </div>
        @endforeach
    @else
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Your cart is empty</h5>
            <p class="text-muted">Add some containers to get started!</p>
            <a href="{{ route('catalog') }}" class="btn btn-primary">
                Continue Shopping
            </a>
        </div>
    @endif
    <!-- JavaScript for alerts -->
    <script>
        document.addEventListener('livewire:load', function() {
            Livewire.on('show-alert', function(data) {
                // You can use any alert library here (Bootstrap, SweetAlert, etc.)
                alert(data.message);
            });

            // Debug: Log when delivery is calculated
            Livewire.on('delivery-calculated', function(data) {
                console.log('Delivery calculated:', data);
            });
        });
    </script>


    <script>
        // document.addEventListener('DOMContentLoaded', function() {
        //     // IDs for buttons and input
        //     const qtyMinus = document.getElementById('qtyMinus');
        //     const qtyPlus = document.getElementById('qtyPlus');
        //     const qtyInput = document.getElementById('qtyInput');

        //     // Order summary IDs
        //     const subtotalEl = document.getElementById('SubtotalSummary');
        //     const deliveryEl = document.getElementById('DeliverySummary');
        //     const taxEl = document.getElementById('TaxSummary');
        //     const totalEl = document.getElementById('TotalSummary');
        //     const ProductPriceTotal = document.getElementById("ProductPriceTotal");
        //     const TotalBtnSummary = document.getElementById("TotalBtnSummary");


        //     // Assuming a base price per unit
        //     const pricePerItem = parseFloat(document.getElementById("ProductPrice").innerHTML.replace(/[^0-9.]/g,
        //         ''));
        //     const deliveryCost = parseFloat(deliveryEl.innerHTML.replace(/[^0-9.]/g, ''));
        //     const taxRate = 0.13;
        //     // alert(pricePerItem); // 333

        //     function updateSummary() {
        //         const qty = parseInt(qtyInput.value) || 1;
        //         const subtotal = pricePerItem * qty;
        //         const tax = (subtotal + deliveryCost) * taxRate;
        //         const total = subtotal + deliveryCost + tax;

        //         subtotalEl.innerHTML = `$${subtotal.toFixed(2)}`;
        //         taxEl.innerHTML = `$${tax.toFixed(2)}`;
        //         totalEl.innerHTML = TotalBtnSummary.innerHTML = `$${total.toFixed(2)}`;
        //         ProductPriceTotal.innerHTML = `$${(pricePerItem * qty).toFixed(2)}`

        //         // Save values to localStorage
        //         localStorage.setItem('orderSubtotal', subtotal.toFixed(2));
        //         localStorage.setItem('orderDelivery', deliveryCost.toFixed(2));
        //         localStorage.setItem('orderTax', tax.toFixed(2));
        //         localStorage.setItem('orderTotal', total.toFixed(2));

        //     }

        //     qtyMinus.addEventListener('click', () => {
        //         let currentVal = parseInt(qtyInput.value) || 1;
        //         if (currentVal > 1) {
        //             qtyInput.value = currentVal - 1;
        //             updateSummary();
        //         }
        //     });

        //     qtyPlus.addEventListener('click', () => {
        //         let currentVal = parseInt(qtyInput.value) || 1;
        //         qtyInput.value = currentVal + 1;
        //         updateSummary();
        //     });

        //     qtyInput.addEventListener('input', () => {
        //         if (qtyInput.value < 1) qtyInput.value = 1;
        //         updateSummary();
        //     });
        // });
    </script>
</div>
