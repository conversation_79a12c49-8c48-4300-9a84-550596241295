<div>
    <!-- Personal Information -->
    <div class="checkout-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-user"></i>
            </div>
            <h5 class="mb-0">Personal Information</h5>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="firstName" class="form-label">First Name *</label>
                <input type="text" class="form-control @error('firstName') is-invalid @enderror" id="firstName"
                       wire:model.live="firstName" placeholder="Enter your first name">
                @error('firstName') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
            <div class="col-md-6 mb-3">
                <label for="lastName" class="form-label">Last Name *</label>
                <input type="text" class="form-control @error('lastName') is-invalid @enderror" id="lastName"
                       wire:model.live="lastName" placeholder="Enter your last name">
                @error('lastName') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="email" class="form-label">Email *</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email"
                           wire:model.live="email" placeholder="Enter your email">
                </div>
                @error('email') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
            <div class="col-md-6 mb-3">
                <label for="phone" class="form-label">Phone Number *</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone"
                           wire:model.live="phone" placeholder="Enter your phone number">
                </div>
                @error('phone') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
        </div>
    </div>

    <!-- Delivery Address -->
    <div class="checkout-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <h5 class="mb-0">Delivery Address</h5>
        </div>

        <div class="row">
            <div class="col-12 mb-3">
                <label for="streetAddress" class="form-label">Street Address *</label>
                <input type="text" class="form-control @error('streetAddress') is-invalid @enderror" id="streetAddress"
                       wire:model.live="streetAddress" placeholder="Enter your street address">
                @error('streetAddress') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="city" class="form-label">City *</label>
                <input type="text" class="form-control @error('city') is-invalid @enderror" id="city"
                       wire:model.live="city" placeholder="Enter your city">
                @error('city') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
            <div class="col-md-4 mb-3">
                <label for="province" class="form-label">Province *</label>
                <input type="text" class="form-control @error('province') is-invalid @enderror" id="province"
                       wire:model.live="province" placeholder="Enter your province">
                @error('province') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
            <div class="col-md-4 mb-3">
                <label for="zipCode" class="form-label">Area Code *</label>
                <input type="text" class="form-control @error('zipCode') is-invalid @enderror" id="zipCode"
                       wire:model.live="zipCode" placeholder="Enter delivery zip code">
                @error('zipCode') <span class="text-danger small">{{ $message }}</span> @enderror
            </div>
        </div>
    </div>

    <!-- Delivery Calculator -->
    <div class="checkout-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-calculator"></i>
            </div>
            <h5 class="mb-0">Delivery Calculator</h5>
        </div>

        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Calculate Delivery Costs:</strong> Enter your complete delivery address above, then click the button below to calculate delivery costs based on distance from our warehouses.
                </div>
                @if($deliveryCost > 0)
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Delivery Cost Calculated:</strong> ${{ number_format($deliveryCost, 2) }}
                        <small class="d-block mt-1">Based on distance from warehouse locations to your delivery address.</small>
                    </div>
                @endif
            </div>
            <div class="col-md-4">
                <button class="btn btn-dark w-100" wire:click="calculateDelivery"
                        @if(empty($streetAddress) || empty($city) || empty($province) || empty($zipCode)) disabled @endif>
                    <i class="fas fa-calculator me-2"></i>
                    Calculate Delivery
                </button>
                @if(empty($streetAddress) || empty($city) || empty($province) || empty($zipCode))
                    <small class="text-muted d-block mt-1">Complete address required</small>
                @endif
            </div>
        </div>
    </div>

    <!-- JavaScript for alerts -->
    <script>
        document.addEventListener('livewire:load', function () {
            Livewire.on('show-alert', function (data) {
                // You can use any alert library here (Bootstrap, SweetAlert, etc.)
                alert(data.message);
            });
        });
    </script>
</div>
