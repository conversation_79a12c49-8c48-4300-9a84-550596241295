<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\CheckoutForm;
use App\Livewire\CheckoutSummary;

class CheckoutFormValidationTest extends TestCase
{
    /** @test */
    public function checkout_form_validates_required_fields()
    {
        Livewire::test(CheckoutForm::class)
            ->assertSet('isFormValid', false)
            ->set('firstName', 'John')
            ->set('lastName', 'Doe')
            ->set('email', '<EMAIL>')
            ->set('phone', '1234567890')
            ->set('streetAddress', '123 Main St')
            ->set('city', 'Toronto')
            ->set('province', 'ON')
            ->set('zipCode', 'M5V 3A8')
            ->assertSet('isFormValid', true);
    }

    /** @test */
    public function checkout_form_shows_validation_errors_for_empty_fields()
    {
        Livewire::test(CheckoutForm::class)
            ->call('validateForm')
            ->assertHasErrors([
                'firstName',
                'lastName', 
                'email',
                'phone',
                'streetAddress',
                'city',
                'province',
                'zipCode'
            ]);
    }

    /** @test */
    public function checkout_form_validates_email_format()
    {
        Livewire::test(CheckoutForm::class)
            ->set('email', 'invalid-email')
            ->call('validateForm')
            ->assertHasErrors(['email']);
    }

    /** @test */
    public function checkout_summary_disables_payment_when_form_invalid()
    {
        Livewire::test(CheckoutSummary::class)
            ->set('isFormValid', false)
            ->set('total', 100)
            ->set('agreeToTerms', true)
            ->call('processPayment')
            ->assertDispatched('show-alert');
    }

    /** @test */
    public function checkout_summary_enables_payment_when_form_valid()
    {
        // Mock session data with all required fields
        session([
            'cart' => [
                [
                    'id' => 1,
                    'name' => 'Test Container',
                    'price' => 100,
                    'qty' => 1,
                    'size' => '20',
                    'type' => 'Purchase',
                    'deliveryCost' => 50
                ]
            ],
            'cart_total' => 150,
            'cart_subtotal' => 100,
            'delivery_cost' => 50,
            'cart_tax' => 19.50
        ]);

        Livewire::test(CheckoutSummary::class)
            ->set('isFormValid', true)
            ->set('total', 150)
            ->set('agreeToTerms', true)
            ->call('processPayment');
            // Note: This would normally proceed to Stripe, but we're just testing validation
    }
}
