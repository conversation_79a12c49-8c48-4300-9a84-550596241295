<?php

namespace App\Livewire;

use Livewire\Component;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class CheckoutSummary extends Component
{
    public $cart = [];
    public $subtotal = 0;
    public $deliveryCost = 0;
    public $tax = 0;
    public $total = 0;
    public $agreeToTerms = false;
    public $isFormValid = false;
    public $validationMessage = '';

    protected $listeners = [
        'form-validation-updated' => 'updateFormValidation',
        'delivery-calculated' => 'updateDeliveryCost'
    ];

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->subtotal = session('cart_subtotal', session('subtotal', 0));
        $this->deliveryCost = session('delivery_cost', 0);
        $this->tax = session('cart_tax', session('tax', 0));
        $this->total = session('cart_total', session('total', 0));

        // If we don't have totals in session, calculate them
        if ($this->total <= 0 && !empty($this->cart)) {
            $this->calculateTotals();
        }

        // Check initial form validation state
        $this->checkInitialFormValidation();
    }

    private function calculateTotals()
    {
        $this->subtotal = collect($this->cart)->sum(function ($item) {
            return $item['price'] * ($item['qty'] ?? 1);
        });

        $this->tax = round(($this->subtotal + $this->deliveryCost) * 0.13, 2);
        $this->total = round($this->subtotal + $this->deliveryCost + $this->tax, 2);

        // Store in session for consistency
        session([
            'cart_subtotal' => $this->subtotal,
            'cart_tax' => $this->tax,
            'cart_total' => $this->total,
            'subtotal' => $this->subtotal,
            'tax' => $this->tax,
            'total' => $this->total
        ]);
    }

    public function updateFormValidation($data)
    {
        $this->isFormValid = $data['isValid'];

        if (!$this->isFormValid) {
            $this->validationMessage = 'Please fill in all required fields before proceeding to payment.';
        } else {
            $this->validationMessage = '';
        }
    }

    public function updateDeliveryCost($data)
    {
        $this->deliveryCost = $data['cost'];
        $this->calculateTotals();
    }

    public function hydrate()
    {
        // Refresh delivery cost from session when component is hydrated
        $this->deliveryCost = session('delivery_cost', 0);
        $this->calculateTotals();

        // Check if form data exists in session and update validation state
        $this->checkInitialFormValidation();
    }

    private function checkInitialFormValidation()
    {
        $checkoutForm = session('checkout_form', []);

        // Check if all required fields are filled
        $requiredFields = ['firstName', 'lastName', 'email', 'phone', 'streetAddress', 'city', 'province', 'zipCode'];
        $isValid = true;

        foreach ($requiredFields as $field) {
            if (empty($checkoutForm[$field])) {
                $isValid = false;
                break;
            }
        }

        $this->isFormValid = $isValid;
        $this->validationMessage = $isValid ? '' : 'Please fill in all required fields before proceeding to payment.';
    }

    public function processPayment()
    {
        // Check form validation first
        if (!$this->isFormValid) {
            $this->dispatch('show-alert', [
                'message' => 'Please fill in all required customer information fields before proceeding to payment.',
                'type' => 'warning'
            ]);
            return;
        }

        if (!$this->agreeToTerms) {
            $this->dispatch('show-alert', [
                'message' => 'Please agree to the terms and conditions.',
                'type' => 'warning'
            ]);
            return;
        }

        if (empty($this->cart)) {
            $this->dispatch('show-alert', [
                'message' => 'Your cart is empty!',
                'type' => 'warning'
            ]);
            return;
        }

        // Note: Delivery cost validation removed - users can proceed without calculating delivery
        // Delivery will be calculated on the checkout page

        // Create Stripe Checkout Session
        return $this->createStripeCheckoutSession();
    }

    private function createStripeCheckoutSession()
    {
        try {
            // Check if Stripe is configured
            $stripeSecret = config('services.stripe.secret');
            if (empty($stripeSecret) || $stripeSecret === 'sk_test_your_stripe_secret_key_here') {
                // For demo/testing purposes, simulate successful payment
                $this->dispatch('show-alert', [
                    'message' => 'Demo mode: Payment would be processed via Stripe. Please configure Stripe keys in .env file for production.',
                    'type' => 'info'
                ]);

                // Simulate successful payment redirect
                return redirect()->route('checkout.success', ['session_id' => 'demo_' . uniqid()]);
            }

            // Set your Stripe secret key
            Stripe::setApiKey($stripeSecret);

            // Prepare line items for Stripe
            $lineItems = [];
            foreach ($this->cart as $item) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => $item['name'],
                            'description' => $item['size'] . 'ft ' . $item['category'] . ' Container - ' . $item['type'],
                        ],
                        'unit_amount' => (int)($item['price'] * 100), // Convert to cents
                    ],
                    'quantity' => (int) $item['qty'],
                ];
            }

            // Add delivery as a separate line item
            if ($this->deliveryCost > 0) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => 'Delivery Fee',
                            'description' => 'Professional delivery service',
                        ],
                        'unit_amount' => (int)($this->deliveryCost * 100),
                    ],
                    'quantity' => 1,
                ];
            }

            // Add tax as a separate line item
            if ($this->tax > 0) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => 'Tax (13%)',
                            'description' => 'Sales tax',
                        ],
                        'unit_amount' => (int)($this->tax * 100),
                    ],
                    'quantity' => 1,
                ];
            }

            // Create Stripe Checkout Session
            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => $lineItems,
                'mode' => 'payment',
                'success_url' => route('checkout.success') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('checkout'),
                'customer_email' => session('checkout_form.email'),
                'metadata' => [
                    'cart_count' => count($this->cart),
                    'delivery_cost' => $this->deliveryCost,
                    'tax' => $this->tax,
                ],
            ]);

            // Store session ID for later verification
            session(['stripe_session_id' => $session->id]);

            // Redirect to Stripe Checkout
            return redirect($session->url);

        } catch (\Exception $e) {
            $this->dispatch('show-alert', [
                'message' => 'Error creating payment session: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }
    
    public function render()
    {
        return view('livewire.checkout-summary');
    }
}
