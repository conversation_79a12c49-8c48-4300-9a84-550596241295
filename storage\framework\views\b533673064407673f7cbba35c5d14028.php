<div class="order-summary sticky-top" style="top: 20px;">
    <h5 class="mb-3">Order Summary</h5>



    <div class="d-flex justify-content-between mb-2">
        <span>Subtotal</span>
        <span id="SubtotalSummary">$<?php echo e(number_format($subtotal, 2)); ?></span>
    </div>

    <div class="d-flex justify-content-between mb-2">
        <span>Delivery</span>
        <span id="DeliverySummary">$<?php echo e(number_format($deliveryCost, 2)); ?></span>
    </div>

    <div class="d-flex justify-content-between mb-3">
        <span>Tax (13%)</span>
        <span id="TaxSummary">$<?php echo e(number_format($tax, 2)); ?></span>
    </div>

    <hr>

    <div class="d-flex justify-content-between mb-3">
        <strong>Total</strong>
        <strong class="h5" id="TotalSummary">$<?php echo e(number_format($total, 2)); ?></strong>
    </div>



    <button class="btn btn-dark w-100 mb-3" wire:click="proceedToCheckout" <?php echo e(empty($cart) ? 'disabled' : ''); ?>>
        Proceed to Checkout <span id="TotalBtnSummary"> $<?php echo e(number_format($total, 2)); ?> </span>
    </button>

    <!--[if BLOCK]><![endif]--><?php if(empty($cart)): ?>
        <p class="text-muted small text-center">Add items to cart to continue.</p>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
</div>

<script>
    // document.addEventListener('livewire:init', () => {
    //     Livewire.on('cart-updated-CartItems', (data) => {
    //         // Update summary values when cart changes
    //         // console.log(data)
    //         document.getElementById('SubtotalSummary').textContent = '$' + data[0].subtotal.toFixed(2);
    //         document.getElementById('TaxSummary').textContent = '$' + data[0].tax.toFixed(2);
    //         document.getElementById('TotalSummary').textContent = '$' + data[0].total.toFixed(2);
    //         document.getElementById('TotalBtnSummary').textContent = '$' + data[0].total.toFixed(2);
    //         // console.log(
    //         //     document.getElementById('SubtotalSummary').textContent,
    //         // document.getElementById('TaxSummary').textContent,
    //         // document.getElementById('TotalSummary').textContent,
    //         // document.getElementById('TotalBtnSummary').textContent,
    //         // )
    //     });
    // });
</script>   
<?php /**PATH C:\xampp\htdocs\containers\resources\views/livewire/cart-summary.blade.php ENDPATH**/ ?>