<div>
    <!-- Personal Information -->
    <div class="checkout-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-user"></i>
            </div>
            <h5 class="mb-0">Personal Information</h5>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="firstName" class="form-label">First Name *</label>
                <input type="text" class="form-control <?php $__errorArgs = ['firstName'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="firstName"
                       wire:model.live="firstName" placeholder="Enter your first name">
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['firstName'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <div class="col-md-6 mb-3">
                <label for="lastName" class="form-label">Last Name *</label>
                <input type="text" class="form-control <?php $__errorArgs = ['lastName'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="lastName"
                       wire:model.live="lastName" placeholder="Enter your last name">
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['lastName'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="email" class="form-label">Email *</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                    <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email"
                           wire:model.live="email" placeholder="Enter your email">
                </div>
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <div class="col-md-6 mb-3">
                <label for="phone" class="form-label">Phone Number *</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                    <input type="tel" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="phone"
                           wire:model.live="phone" placeholder="Enter your phone number">
                </div>
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>

    <!-- Delivery Address -->
    <div class="checkout-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <h5 class="mb-0">Delivery Address</h5>
        </div>

        <div class="row">
            <div class="col-12 mb-3">
                <label for="streetAddress" class="form-label">Street Address *</label>
                <input type="text" class="form-control <?php $__errorArgs = ['streetAddress'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="streetAddress"
                       wire:model.live="streetAddress" placeholder="Enter your street address">
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['streetAddress'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="city" class="form-label">City *</label>
                <input type="text" class="form-control <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="city"
                       wire:model.live="city" placeholder="Enter your city">
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <div class="col-md-4 mb-3">
                <label for="province" class="form-label">Province *</label>
                <input type="text" class="form-control <?php $__errorArgs = ['province'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="province"
                       wire:model.live="province" placeholder="Enter your province">
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['province'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <div class="col-md-4 mb-3">
                <label for="zipCode" class="form-label">Area Code *</label>
                <input type="text" class="form-control <?php $__errorArgs = ['zipCode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="zipCode"
                       wire:model.live="zipCode" placeholder="Enter delivery zip code">
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['zipCode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger small"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>

    <!-- Delivery Calculator -->
    <div class="checkout-section delivery-calculator-section">
        <div class="section-header">
            <div class="section-icon bg-dark">
                <i class="fas fa-shipping-fast text-white"></i>
            </div>
            <div>
                <h5 class="mb-1">Delivery Cost Calculator</h5>
                <p class="text-muted small mb-0">Get accurate delivery pricing based on your location</p>
            </div>
        </div>

        <div class="delivery-calculator-content">
            <!-- Status Cards -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="delivery-status-card">
                        <div class="d-flex align-items-center">
                            <div class="status-icon me-3">
                                <!--[if BLOCK]><![endif]--><?php if(empty($streetAddress) || empty($city) || empty($province) || empty($zipCode)): ?>
                                    <i class="fas fa-map-marker-alt text-warning"></i>
                                <?php else: ?>
                                    <i class="fas fa-check-circle text-success"></i>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <div>
                                <h6 class="mb-1">Delivery Address</h6>
                                <small class="text-muted">
                                    <!--[if BLOCK]><![endif]--><?php if(empty($streetAddress) || empty($city) || empty($province) || empty($zipCode)): ?>
                                        Complete your address above
                                    <?php else: ?>
                                        Address verified ✓
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="delivery-status-card">
                        <div class="d-flex align-items-center">
                            <div class="status-icon me-3">
                                <!--[if BLOCK]><![endif]--><?php if($deliveryCost > 0): ?>
                                    <i class="fas fa-dollar-sign text-success"></i>
                                <?php else: ?>
                                    <i class="fas fa-calculator text-muted"></i>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <div>
                                <h6 class="mb-1">Delivery Cost</h6>
                                <small class="text-muted">
                                    <!--[if BLOCK]><![endif]--><?php if($deliveryCost > 0): ?>
                                        $<?php echo e(number_format($deliveryCost, 2)); ?> calculated
                                    <?php else: ?>
                                        Not calculated yet
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Calculation Button and Results -->
            <div class="delivery-calculation-area">
                <!--[if BLOCK]><![endif]--><?php if($deliveryCost > 0): ?>
                    <div class="delivery-result-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="result-icon me-3">
                                        <i class="fas fa-check-circle text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 text-success">Delivery Cost Calculated</h6>
                                        <p class="mb-0 text-muted small">
                                            Total delivery cost: <strong class="text-dark">$<?php echo e(number_format($deliveryCost, 2)); ?></strong>
                                        </p>
                                        <small class="text-muted">Based on distance from our warehouse locations</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-outline-dark btn-sm" wire:click="calculateDelivery">
                                    <i class="fas fa-redo me-1"></i>
                                    Recalculate
                                </button>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="delivery-calculate-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="calculate-icon me-3">
                                        <i class="fas fa-route text-dark"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Ready to Calculate Delivery</h6>
                                        <p class="mb-0 text-muted small">
                                            We'll calculate the exact delivery cost based on the distance from our nearest warehouse to your address.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-dark w-100 delivery-calc-btn"
                                        wire:click="calculateDelivery"
                                        <?php if(empty($streetAddress) || empty($city) || empty($province) || empty($zipCode) || $isCalculatingDelivery): ?> disabled <?php endif; ?>>
                                    <!--[if BLOCK]><![endif]--><?php if($isCalculatingDelivery): ?>
                                        <i class="fas fa-spinner fa-spin me-2"></i>
                                        Calculating...
                                    <?php else: ?>
                                        <i class="fas fa-calculator me-2"></i>
                                        Calculate Delivery
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </button>
                                <!--[if BLOCK]><![endif]--><?php if(empty($streetAddress) || empty($city) || empty($province) || empty($zipCode)): ?>
                                    <small class="text-muted d-block mt-2 text-center">Complete address required</small>
                                <?php elseif($isCalculatingDelivery): ?>
                                    <small class="text-dark d-block mt-2 text-center">
                                        <i class="fas fa-map-marked-alt me-1"></i>
                                        Calculating distance from warehouse...
                                    </small>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Features -->
            <div class="delivery-features mt-4">
                <div class="row">
                    <div class="col-md-4">
                        <div class="feature-item">
                            <i class="fas fa-truck text-dark me-2"></i>
                            <small class="text-muted">Professional delivery team</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-item">
                            <i class="fas fa-clock text-dark me-2"></i>
                            <small class="text-muted">Scheduled delivery windows</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-item">
                            <i class="fas fa-shield-alt text-dark me-2"></i>
                            <small class="text-muted">Fully insured transport</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for alerts -->
    <script>
        document.addEventListener('livewire:load', function () {
            Livewire.on('show-alert', function (data) {
                // You can use any alert library here (Bootstrap, SweetAlert, etc.)
                alert(data.message);
            });
        });
    </script>

    <!-- Custom Styles for Delivery Calculator -->
    <style>
        .delivery-calculator-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        .delivery-calculator-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #000, #000);
        }

        .delivery-calculator-content {
            padding: 1rem 0;
        }

        .delivery-status-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .delivery-status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .delivery-result-card {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .delivery-calculate-card {
            background: #ffffff;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .delivery-calculate-card:hover {
            border-color: #000;
            background: #f8f9ff;
        }

        .result-icon, .calculate-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .delivery-calc-btn {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,123,255,0.2);
        }

        .delivery-calc-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,123,255,0.3);
        }

        .delivery-calc-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .delivery-features {
            border-top: 1px solid #e9ecef;
            padding-top: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .section-icon.bg-dark {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            box-shadow: 0 4px 12px rgba(0,123,255,0.2);
        }

        @media (max-width: 768px) {
            .delivery-status-card {
                margin-bottom: 1rem;
            }

            .delivery-calc-btn {
                margin-top: 1rem;
            }
        }
    </style>
</div>
<?php /**PATH C:\xampp\htdocs\containers\resources\views/livewire/checkout-form.blade.php ENDPATH**/ ?>