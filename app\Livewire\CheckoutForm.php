<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Container;
use Illuminate\Support\Facades\Http;

class CheckoutForm extends Component
{
    // Personal Information
    public $firstName = '';
    public $lastName = '';
    public $email = '';
    public $phone = '';

    // Delivery Address
    public $streetAddress = '';
    public $city = '';
    public $province = '';
    public $zipCode = '';

    // Validation state
    public $isFormValid = false;
    public $isCalculatingDelivery = false;

    // Order Data
    public $cart = [];
    public $subtotal = 0;
    public $deliveryCost = 0;
    public $tax = 0;
    public $total = 0;
    public $agreeToTerms = false;

    protected $rules = [
        'firstName' => 'required|string|max:255',
        'lastName' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:20',
        'streetAddress' => 'required|string|max:255',
        'city' => 'required|string|max:255',
        'province' => 'required|string|max:255',
        'zipCode' => 'required|string|max:10',
    ];

    protected $messages = [
        'firstName.required' => 'First name is required.',
        'lastName.required' => 'Last name is required.',
        'email.required' => 'Email address is required.',
        'email.email' => 'Please enter a valid email address.',
        'phone.required' => 'Phone number is required.',
        'streetAddress.required' => 'Street address is required.',
        'city.required' => 'City is required.',
        'province.required' => 'Province is required.',
        'zipCode.required' => 'Area code is required.',
    ];

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->subtotal = session('cart_subtotal', 0);
        $this->deliveryCost = session('delivery_cost', 0);
        $this->tax = session('cart_tax', 0);
        $this->total = $this->subtotal + $this->deliveryCost + $this->tax;

        // Load form data from session if available
        $checkoutForm = session('checkout_form', []);

        $this->firstName = $checkoutForm['firstName'] ?? '';
        $this->lastName = $checkoutForm['lastName'] ?? '';
        $this->email = $checkoutForm['email'] ?? '';
        $this->phone = $checkoutForm['phone'] ?? '';
        $this->streetAddress = $checkoutForm['streetAddress'] ?? '';
        $this->city = $checkoutForm['city'] ?? '';
        $this->province = $checkoutForm['province'] ?? '';
        $this->zipCode = $checkoutForm['zipCode'] ?? session('zip_code', '');

        // Save zip code to main session key if not empty
        if ($this->zipCode !== '') {
            session(['zip_code' => $this->zipCode]);
        }

        // Check initial validation state
        $this->checkFormValidation();
    }

    public function hydrate()
    {
        // Restore form data from session when component is rehydrated
        $checkoutForm = session('checkout_form', []);

        if (!empty($checkoutForm)) {
            $this->firstName = $checkoutForm['firstName'] ?? $this->firstName;
            $this->lastName = $checkoutForm['lastName'] ?? $this->lastName;
            $this->email = $checkoutForm['email'] ?? $this->email;
            $this->phone = $checkoutForm['phone'] ?? $this->phone;
            $this->streetAddress = $checkoutForm['streetAddress'] ?? $this->streetAddress;
            $this->city = $checkoutForm['city'] ?? $this->city;
            $this->province = $checkoutForm['province'] ?? $this->province;
            $this->zipCode = $checkoutForm['zipCode'] ?? $this->zipCode;
        }

        // Also refresh delivery cost and totals from session
        $this->deliveryCost = session('delivery_cost', 0);
        $this->subtotal = session('cart_subtotal', 0);
        $this->tax = session('cart_tax', 0);
        $this->total = $this->subtotal + $this->deliveryCost + $this->tax;

        // Trigger form validation update to enable/disable Stripe button
        $this->checkFormValidation();
    }

    public function updated($propertyName)
    {
        // Save form data to session as user types
        $this->saveFormDataToSession();

        // Also save zip code to main session for consistency across components
        if ($propertyName == 'zipCode') {
            session(['zip_code' => $this->zipCode]);
        }

        // Auto-recalculate delivery if address fields change and delivery was previously calculated
        $addressFields = ['streetAddress', 'city', 'province', 'zipCode'];
        if (in_array($propertyName, $addressFields) && $this->deliveryCost > 0) {
            // Check if all address fields are filled
            if (!empty($this->streetAddress) && !empty($this->city) && !empty($this->province) && !empty($this->zipCode)) {
                // Show a brief message that we're auto-recalculating
                $this->dispatch('show-alert', [
                    'message' => 'Address changed - recalculating delivery cost...',
                    'type' => 'info'
                ]);
                $this->calculateDelivery();
            }
        }

        // Validate the specific field that was updated
        $this->validateOnly($propertyName);

        // Check overall form validation and emit event
        $this->checkFormValidation();
    }

    private function saveFormDataToSession()
    {
        session([
            'checkout_form' => [
                'firstName' => $this->firstName,
                'lastName' => $this->lastName,
                'email' => $this->email,
                'phone' => $this->phone,
                'streetAddress' => $this->streetAddress,
                'city' => $this->city,
                'province' => $this->province,
                'zipCode' => $this->zipCode,
            ]
        ]);
    }

    public function clearFormData()
    {
        // Clear form data from session (useful after successful payment)
        session()->forget('checkout_form');

        // Reset component properties
        $this->firstName = '';
        $this->lastName = '';
        $this->email = '';
        $this->phone = '';
        $this->streetAddress = '';
        $this->city = '';
        $this->province = '';
        $this->zipCode = '';
        $this->deliveryCost = 0;
    }

    public function checkFormValidation()
    {
        try {
            $this->validate();
            $this->isFormValid = true;
            $this->dispatch('form-validation-updated', ['isValid' => true]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->isFormValid = false;
            $this->dispatch('form-validation-updated', ['isValid' => false]);
        }
    }

    public function validateForm()
    {
        $this->validate();
        $this->checkFormValidation();

        if ($this->isFormValid) {
            $this->dispatch('show-alert', [
                'message' => 'All fields are valid! You can now proceed to payment.',
                'type' => 'success'
            ]);
        }
    }

    public function calculateDelivery()
    {
        // Set loading state
        $this->isCalculatingDelivery = true;

        // Validate that all address fields are filled
        if (empty($this->streetAddress) || empty($this->city) || empty($this->province) || empty($this->zipCode)) {
            $this->isCalculatingDelivery = false;
            $this->dispatch('show-alert', [
                'message' => 'Please fill in all address fields before calculating delivery.',
                'type' => 'warning'
            ]);
            return;
        }

        if (empty($this->cart)) {
            $this->dispatch('show-alert', [
                'message' => 'Your cart is empty!',
                'type' => 'warning'
            ]);
            return;
        }

        $totalDelivery = 0;

        // Calculate delivery for each item in cart
        foreach ($this->cart as $item) {
            $container = Container::with('warehouse', 'size')->find($item['id']);
            if (!$container || !$container->warehouse) {
                continue;
            }

            // Build destination address
            $destination = urlencode("{$this->streetAddress}, {$this->city}, {$this->province}, {$this->zipCode}");
            $origin = urlencode($container->warehouse->location);

            // Get Google Maps API key
            $apiKey = config('services.googlemaps.key');

            if (empty($apiKey)) {
                $this->dispatch('show-alert', [
                    'message' => 'Google Maps API key not configured. Please contact support.',
                    'type' => 'error'
                ]);
                return;
            }

            // Call Google Maps Distance Matrix API
            $url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins={$origin}&destinations={$destination}&key={$apiKey}";

            try {
                $response = Http::timeout(10)->get($url);
                $distance_km = 0;

                if ($response->ok()) {
                    $data = $response->json();
                    if (isset($data['rows'][0]['elements'][0]['distance']['value'])) {
                        $distance_km = $data['rows'][0]['elements'][0]['distance']['value'] / 1000; // Convert meters to km
                    } else {
                        // If no distance found, use a default distance (e.g., 50km)
                        $distance_km = 50;
                    }
                } else {
                    // If API fails, use a default distance
                    $distance_km = 50;
                }

                // Calculate delivery cost based on distance and container size
                $cost_per_km = $container->size ? $container->size->cost_per_km : 2.5;
                $delivery = round($distance_km * $cost_per_km, 2);
                $totalDelivery += $delivery * ($item['qty'] ?? 1);

            } catch (\Exception $e) {
                // If API fails, use a fallback calculation
                $cost_per_km = $container->size ? $container->size->cost_per_km : 2.5;
                $fallback_distance = 50; // 50km default
                $delivery = round($fallback_distance * $cost_per_km, 2);
                $totalDelivery += $delivery * ($item['qty'] ?? 1);
            }
        }

        // Update delivery cost
        $this->deliveryCost = round($totalDelivery, 2);

        // Save to session
        session(['delivery_cost' => $this->deliveryCost]);

        // Recalculate totals
        $this->tax = round(($this->subtotal + $this->deliveryCost) * 0.13, 2);
        $this->total = round($this->subtotal + $this->deliveryCost + $this->tax, 2);

        // Save updated totals to session
        session([
            'cart_tax' => $this->tax,
            'cart_total' => $this->total
        ]);

        // Dispatch success message
        $this->dispatch('show-alert', [
            'message' => 'Delivery cost calculated successfully! Total delivery cost: $' . number_format($this->deliveryCost, 2),
            'type' => 'success'
        ]);

        // Save form data to session
        $this->saveFormDataToSession();

        // Dispatch event to update other components
        $this->dispatch('delivery-calculated', ['cost' => $this->deliveryCost]);

        // Trigger form validation update
        $this->checkFormValidation();

        // Reset loading state
        $this->isCalculatingDelivery = false;
    }

    public function getRequiredFieldsProperty()
    {
        return [
            'firstName' => 'First Name',
            'lastName' => 'Last Name',
            'email' => 'Email',
            'phone' => 'Phone Number',
            'streetAddress' => 'Street Address',
            'city' => 'City',
            'province' => 'Province',
            'zipCode' => 'Area Code'
        ];
    }

    public function getMissingFieldsProperty()
    {
        $missing = [];
        foreach ($this->getRequiredFieldsProperty() as $field => $label) {
            if (empty($this->$field)) {
                $missing[] = $label;
            }
        }
        return $missing;
    }

    public function render()
    {
        return view('livewire.checkout-form');
    }
}
