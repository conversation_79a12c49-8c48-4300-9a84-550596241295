<?php

namespace App\Livewire;

use Livewire\Component;

class CheckoutForm extends Component
{
    // Personal Information
    public $firstName = '';
    public $lastName = '';
    public $email = '';
    public $phone = '';

    // Delivery Address
    public $streetAddress = '';
    public $city = '';
    public $province = '';
    public $zipCode = '';

    // Validation state
    public $isFormValid = false;

    // Order Data
    public $cart = [];
    public $subtotal = 0;
    public $deliveryCost = 0;
    public $tax = 0;
    public $total = 0;
    public $agreeToTerms = false;

    protected $rules = [
        'firstName' => 'required|string|max:255',
        'lastName' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:20',
        'streetAddress' => 'required|string|max:255',
        'city' => 'required|string|max:255',
        'province' => 'required|string|max:255',
        'zipCode' => 'required|string|max:10',
    ];

    protected $messages = [
        'firstName.required' => 'First name is required.',
        'lastName.required' => 'Last name is required.',
        'email.required' => 'Email address is required.',
        'email.email' => 'Please enter a valid email address.',
        'phone.required' => 'Phone number is required.',
        'streetAddress.required' => 'Street address is required.',
        'city.required' => 'City is required.',
        'province.required' => 'Province is required.',
        'zipCode.required' => 'Area code is required.',
    ];

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->subtotal = session('cart_subtotal', 0);
        $this->deliveryCost = session('delivery_cost', 0);
        $this->tax = session('cart_tax', 0);
        $this->total = $this->subtotal + $this->deliveryCost + $this->tax;

        $this->firstName = '';
        $this->lastName = '';
        $this->email = '';
        $this->phone = '';
        $this->streetAddress = '';
        $this->city = '';
        $this->province = '';

        // Fetch zip code from checkout_form
        $this->zipCode = session('checkout_form')['zipCode'] ?? '';

        // Save to main session key if not empty
        if ($this->zipCode !== '') {
            session(['zip_code' => $this->zipCode]);
        }

        // Check initial validation state
        $this->checkFormValidation();
    }

    public function updated($propertyName)
    {
        // Save form data to session as user types
        session(["checkout_form.{$propertyName}" => $this->$propertyName]);

        // Also save zip code to main session for consistency across components
        if ($propertyName == 'zipCode') {
            session(['zip_code' => $this->zipCode]);
        }

        // Validate the specific field that was updated
        $this->validateOnly($propertyName);

        // Check overall form validation and emit event
        $this->checkFormValidation();
    }

    public function checkFormValidation()
    {
        try {
            $this->validate();
            $this->isFormValid = true;
            $this->dispatch('form-validation-updated', ['isValid' => true]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->isFormValid = false;
            $this->dispatch('form-validation-updated', ['isValid' => false]);
        }
    }

    public function validateForm()
    {
        $this->validate();
        $this->checkFormValidation();

        if ($this->isFormValid) {
            $this->dispatch('show-alert', [
                'message' => 'All fields are valid! You can now proceed to payment.',
                'type' => 'success'
            ]);
        }
    }

    public function getRequiredFieldsProperty()
    {
        return [
            'firstName' => 'First Name',
            'lastName' => 'Last Name',
            'email' => 'Email',
            'phone' => 'Phone Number',
            'streetAddress' => 'Street Address',
            'city' => 'City',
            'province' => 'Province',
            'zipCode' => 'Area Code'
        ];
    }

    public function getMissingFieldsProperty()
    {
        $missing = [];
        foreach ($this->getRequiredFieldsProperty() as $field => $label) {
            if (empty($this->$field)) {
                $missing[] = $label;
            }
        }
        return $missing;
    }

    public function render()
    {
        return view('livewire.checkout-form');
    }
}
